# Уеб сайт с новини и галерия

## Инсталация

1. Копирайте файловете в Apache документната директория
2. Създайте MySQL база данни и изпълнете `database/schema.sql`
3. Конфигурирайте `includes/config.php` с вашите данни
4. Задайте права за писане на `uploads/` директорията
5. Създайте първия собственик чрез `admin/create_owner.php`

## Структура
- `public/` - публични файлове (CSS, JS, изображения)
- `includes/` - PHP класове и конфигурация
- `templates/` - HTML шаблони
- `admin/` - административен панел
- `uploads/` - качени файлове
- `database/` - SQL схема

## Роли
- **Потребител**: създава/редактира новини, качва снимки
- **Админ**: + изтрива новини, създава кодове, редактира сайт
- **Собственик**: + деактивира потребители, under construction режим