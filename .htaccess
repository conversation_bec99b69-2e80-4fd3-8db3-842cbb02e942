RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Protect sensitive files
<Files "*.php">
    Order allow,deny
    Allow from all
</Files>

<FilesMatch "^(config|auth|functions)\.php$">
    Order deny,allow
    Deny from all
</FilesMatch>