<?php
require_once 'config.php';

class Auth {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    public function login($username, $password) {
        $stmt = $this->pdo->prepare("SELECT * FROM users WHERE username = ? AND is_active = 1");
        $stmt->execute([$username]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password_hash'])) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            return true;
        }
        return false;
    }
    
    public function register($username, $email, $password, $registration_code) {
        // Проверка на кода
        $stmt = $this->pdo->prepare("SELECT * FROM registration_codes WHERE code = ? AND is_used = 0");
        $stmt->execute([$registration_code]);
        $code = $stmt->fetch();
        
        if (!$code) return false;
        
        $password_hash = password_hash($password, PASSWORD_DEFAULT);
        
        $this->pdo->beginTransaction();
        try {
            // Създаване на потребител
            $stmt = $this->pdo->prepare("INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)");
            $stmt->execute([$username, $email, $password_hash]);
            $user_id = $this->pdo->lastInsertId();
            
            // Маркиране на кода като използван
            $stmt = $this->pdo->prepare("UPDATE registration_codes SET is_used = 1, used_by = ? WHERE id = ?");
            $stmt->execute([$user_id, $code['id']]);
            
            $this->pdo->commit();
            return true;
        } catch (Exception $e) {
            $this->pdo->rollback();
            return false;
        }
    }
    
    public function isLoggedIn() {
        return isset($_SESSION['user_id']);
    }
    
    public function hasRole($required_role) {
        if (!$this->isLoggedIn()) return false;
        
        $roles = ['Потребител' => 1, 'Админ' => 2, 'Собственик' => 3];
        $user_level = $roles[$_SESSION['role']] ?? 0;
        $required_level = $roles[$required_role] ?? 0;
        
        return $user_level >= $required_level;
    }
    
    public function logout() {
        session_destroy();
    }
}

$auth = new Auth($pdo);
?>