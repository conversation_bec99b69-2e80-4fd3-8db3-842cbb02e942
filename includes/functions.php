<?php
function checkUnderConstruction($pdo, $auth) {
    $stmt = $pdo->prepare("SELECT setting_value FROM site_settings WHERE setting_key = 'under_construction'");
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result && $result['setting_value'] == '1') {
        if (!$auth->hasRole('Собственик')) {
            include 'templates/under_construction.php';
            exit;
        }
    }
}

function getSiteSetting($pdo, $key) {
    $stmt = $pdo->prepare("SELECT setting_value FROM site_settings WHERE setting_key = ?");
    $stmt->execute([$key]);
    $result = $stmt->fetch();
    return $result ? $result['setting_value'] : '';
}

function uploadImage($file, $max_size = 2097152) { // 2MB
    if ($file['error'] !== UPLOAD_ERR_OK) return false;
    if ($file['size'] > $max_size) return false;
    
    $allowed = ['jpg', 'jpeg', 'png', 'gif'];
    $ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($ext, $allowed)) return false;
    
    $filename = uniqid() . '.' . $ext;
    $path = UPLOAD_DIR . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $path)) {
        return $filename;
    }
    return false;
}
?>