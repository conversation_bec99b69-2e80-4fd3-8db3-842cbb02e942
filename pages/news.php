<?php
include 'templates/header.php';

$stmt = $pdo->prepare("SELECT n.*, u.username FROM news n JOIN users u ON n.author_id = u.id ORDER BY n.created_at DESC");
$stmt->execute();
$news = $stmt->fetchAll();
?>

<h1>Новини</h1>

<div class="row">
    <?php foreach ($news as $article): ?>
        <div class="col-md-4 mb-4">
            <div class="card">
                <?php if ($article['small_image']): ?>
                    <img src="<?= UPLOAD_URL . $article['small_image'] ?>" class="card-img-top" alt="<?= htmlspecialchars($article['title']) ?>">
                <?php endif; ?>
                <div class="card-body">
                    <h5 class="card-title"><?= htmlspecialchars($article['title']) ?></h5>
                    <p class="card-text"><?= htmlspecialchars($article['summary']) ?></p>
                    <small class="text-muted">От <?= htmlspecialchars($article['username']) ?> на <?= date('d.m.Y', strtotime($article['created_at'])) ?></small>
                    <br>
                    <a href="/news/<?= $article['id'] ?>" class="btn btn-primary mt-2">Прочети повече</a>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<?php include 'templates/footer.php'; ?>