<?php
include 'templates/header.php';

if ($auth->isLoggedIn()) {
    header('Location: /admin');
    exit;
}

$error = '';
if ($_POST) {
    if ($auth->login($_POST['username'], $_POST['password'])) {
        header('Location: /admin');
        exit;
    } else {
        $error = 'Грешно потребителско име или парола';
    }
}
?>

<div class="row justify-content-center">
    <div class="col-md-6">
        <h2>Вход</h2>
        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>
        <form method="POST">
            <div class="mb-3">
                <label class="form-label">Потребителско име</label>
                <input type="text" name="username" class="form-control" required>
            </div>
            <div class="mb-3">
                <label class="form-label">Парола</label>
                <input type="password" name="password" class="form-control" required>
            </div>
            <button type="submit" class="btn btn-primary">Вход</button>
        </form>
    </div>
</div>

<?php include 'templates/footer.php'; ?>