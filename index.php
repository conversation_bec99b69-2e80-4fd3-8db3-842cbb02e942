<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

switch ($uri) {
    case '/':
    case '/home':
        include 'pages/home.php';
        break;
    case '/about':
        include 'pages/about.php';
        break;
    case '/contacts':
        include 'pages/contacts.php';
        break;
    case '/gallery':
        include 'pages/gallery.php';
        break;
    case '/news':
        include 'pages/news.php';
        break;
    case '/sitemap':
        include 'pages/sitemap.php';
        break;
    case '/login':
        include 'pages/login.php';
        break;
    case '/register':
        include 'pages/register.php';
        break;
    case '/logout':
        $auth->logout();
        header('Location: /');
        break;
    default:
        if (preg_match('/^\/news\/(\d+)$/', $uri, $matches)) {
            $_GET['id'] = $matches[1];
            include 'pages/news_single.php';
        } elseif (strpos($uri, '/admin') === 0) {
            include 'admin/index.php';
        } else {
            http_response_code(404);
            include 'pages/404.php';
        }
}
?>