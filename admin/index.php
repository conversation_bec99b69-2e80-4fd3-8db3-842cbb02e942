<?php
require_once '../includes/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

if (!$auth->isLoggedIn()) {
    header('Location: /login');
    exit;
}

$uri = str_replace('/admin', '', parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH));

switch ($uri) {
    case '':
    case '/':
        include 'dashboard.php';
        break;
    case '/news':
        include 'news_manage.php';
        break;
    case '/gallery':
        include 'gallery_manage.php';
        break;
    case '/users':
        if ($auth->hasRole('Собственик')) {
            include 'users_manage.php';
        } else {
            header('HTTP/1.0 403 Forbidden');
        }
        break;
    case '/settings':
        if ($auth->hasRole('Админ')) {
            include 'settings.php';
        } else {
            header('HTTP/1.0 403 Forbidden');
        }
        break;
    default:
        header('HTTP/1.0 404 Not Found');
}
?>